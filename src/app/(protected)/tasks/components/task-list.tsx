"use client";

import { useState, useRef, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { Task, TaskSortOption } from "@/lib/db";
import { useReorderTasksMutation, useAddTaskMutation } from "@/lib/queries";
import { TaskItem } from "./task-item";
import { QuickAddTask } from "./quick-add-task";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Check, X, Plus } from "lucide-react";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
} from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  useDragSensors,
  customCollisionDetection,
  dragMeasuring,
  createDragStartHand<PERSON>,
  createDragOver<PERSON><PERSON><PERSON>,
  createDragEnd<PERSON><PERSON><PERSON>,
} from "@/lib/drag-and-drop";

interface TaskListProps {
  tasks: Task[]; // Parent tasks only (filtered)
  allTasks?: Task[]; // All tasks including subtasks (for TaskItem to find its subtasks)
  onTaskUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onTaskDeleted: (deletedTaskId?: string) => void;
  onTasksReordered: (tasks: Task[]) => void;
  sortOption: TaskSortOption;
  listId: string;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  showQuickAdd?: boolean;
  onAddTaskClick?: () => void;
  isInlineEditEnabled?: boolean;
  activeActionIconsTaskId?: string | null;
  onActionIconsChange?: (taskId: string | null) => void;
  onNavigateToTask?: (task: Task) => void; // Callback to switch to a different task
  isTagFiltered?: boolean; // Whether we're in tag-filtered view
  onDragStart?: () => void; // Callback when drag operation starts
  currentSpaceId?: string; // Current space ID for filtering lists
}

export function TaskList({
  tasks,
  allTasks,
  onTaskUpdated,
  onTaskDeleted,
  onTasksReordered,
  sortOption,
  listId,
  listColor,
  taskCounts,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  showQuickAdd = false,
  onAddTaskClick,
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  onNavigateToTask,
  isTagFiltered = false,
  onDragStart,
  currentSpaceId,
}: TaskListProps) {
  const user = useUser();
  // Local state for drag and drop visual feedback only
  const [isDragging, setIsDragging] = useState(false);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const lastDragOverRef = useRef<string | null>(null);

  // Inline task creation state
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState("");
  const newTaskInputRef = useRef<HTMLInputElement>(null);

  // TanStack Query mutations
  const reorderTasksMutation = useReorderTasksMutation(listId, sortOption);
  const addTaskMutation = useAddTaskMutation(listId, sortOption);

  // Focus effect for new task input
  useEffect(() => {
    if (isAddingTask && newTaskInputRef.current) {
      newTaskInputRef.current.focus();
    }
  }, [isAddingTask]);

  // Inline task creation handler
  const handleAddTask = async () => {
    if (!user || !newTaskTitle.trim()) return;

    try {
      const result = await addTaskMutation.mutateAsync({
        userId: user.id,
        title: newTaskTitle.trim(),
      });

      if (result) {
        setNewTaskTitle("");
        setIsAddingTask(false);
        onTaskUpdated(result, false);
      }
    } catch (error) {
      console.error('Error creating task:', error);
    }
  };

  // Use standardized drag and drop configuration
  const sensors = useDragSensors();





  // Use standardized drag handlers
  const handleDragStart = createDragStartHandler(
    setIsDragging,
    setActiveTask,
    tasks,
    onDragStart,
    undefined, // No longer need isReorderingRef
    lastDragOverRef
  );

  const handleDragOver = createDragOverHandler(lastDragOverRef);

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    // Clean up drag state
    setIsDragging(false);
    setActiveTask(null);
    lastDragOverRef.current = null;

    // Validate drag operation - only proceed if there's actually a position change
    if (!over || !active || active.id === over.id) {
      return;
    }

    // Find the indices of the dragged and target tasks
    const oldIndex = tasks.findIndex((task) => task.id === active.id);
    const newIndex = tasks.findIndex((task) => task.id === over.id);

    // Validate indices
    if (oldIndex === -1 || newIndex === -1 || oldIndex === newIndex) {
      return;
    }

    // Calculate new task order
    const newTasks = arrayMove(tasks, oldIndex, newIndex);
    const taskIds = newTasks.map(task => task.id);

    // Call the parent callback for undo/redo tracking
    onTasksReordered(newTasks);

    // Perform the server update with optimistic updates handled by TanStack Query
    if (user) {
      reorderTasksMutation.mutate({ userId: user.id, taskIds });
    }
  };

  return (
    <div className={`space-y-2 ${isDragging ? 'dnd-context-dragging' : ''}`}>
      {sortOption === "position" ? (
        <DndContext
          id={`parent-tasks-${listId}`}
          sensors={sensors}
          collisionDetection={customCollisionDetection}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis]}
        >
          <SortableContext
            items={tasks.map((task) => task.id)}
            strategy={verticalListSortingStrategy}
          >
            {tasks.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onUpdated={onTaskUpdated}
                onDeleted={onTaskDeleted}
                isDraggable={true}
                isAnyTaskDragging={isDragging}
                listId={listId}
                sortOption={sortOption}
                listColor={listColor}
                taskCounts={taskCounts}
                taskMode={taskMode}
                selectedTaskIds={selectedTaskIds}
                onTaskSelectionChange={onTaskSelectionChange}
                lastSelectedTaskId={lastSelectedTaskId}
                isInlineEditEnabled={isInlineEditEnabled}
                activeActionIconsTaskId={activeActionIconsTaskId}
                onActionIconsChange={onActionIconsChange}
                allTasks={allTasks || tasks}
                onNavigateToTask={onNavigateToTask}
                isTagFiltered={isTagFiltered}
                currentSpaceId={currentSpaceId}
              />
            ))}
          </SortableContext>

          {/* Drag Overlay to prevent card compression and maintain visual consistency */}
          <DragOverlay
            adjustScale={false}
            dropAnimation={null}
            style={{
              transformOrigin: '0 0',
              transition: 'none',
            }}
          >
            {activeTask ? (
              <div className="drag-overlay task-drag-overlay">
                <TaskItem
                  task={activeTask}
                  onUpdated={() => {}}
                  onDeleted={() => {}}
                  isDraggable={false}
                  isAnyTaskDragging={true}
                  listId={listId}
                  sortOption={sortOption}
                  listColor={listColor}
                  taskCounts={taskCounts}
                  taskMode={taskMode}
                  selectedTaskIds={selectedTaskIds}
                  onTaskSelectionChange={onTaskSelectionChange}
                  lastSelectedTaskId={lastSelectedTaskId}
                  forceSelected={true}
                  isInlineEditEnabled={isInlineEditEnabled}
                  activeActionIconsTaskId={activeActionIconsTaskId}
                  onActionIconsChange={onActionIconsChange}
                  allTasks={allTasks || tasks}
                  onNavigateToTask={onNavigateToTask}
                  isTagFiltered={isTagFiltered}
                  currentSpaceId={currentSpaceId}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      ) : (
        // Non-draggable list for when sorted by title or due date
        tasks.map((task) => (
          <TaskItem
            key={task.id}
            task={task}
            onUpdated={onTaskUpdated}
            onDeleted={onTaskDeleted}
            isDraggable={false}
            isAnyTaskDragging={false}
            listId={listId}
            sortOption={sortOption}
            listColor={listColor}
            taskCounts={taskCounts}
            taskMode={taskMode}
            selectedTaskIds={selectedTaskIds}
            onTaskSelectionChange={onTaskSelectionChange}
            lastSelectedTaskId={lastSelectedTaskId}
            isInlineEditEnabled={isInlineEditEnabled}
            activeActionIconsTaskId={activeActionIconsTaskId}
            onActionIconsChange={onActionIconsChange}
            allTasks={allTasks || tasks}
            onNavigateToTask={onNavigateToTask}
            isTagFiltered={isTagFiltered}
            currentSpaceId={currentSpaceId}
          />
        ))
      )}

      {/* Quick Add Task Interface */}
      {showQuickAdd && onAddTaskClick && (
        <QuickAddTask
          onAddTaskClick={onAddTaskClick}
        />
      )}

      {/* Inline Task Creation - Show after tasks when not in tag-filtered view */}
      {!isTagFiltered && tasks.length > 0 && (
        <div className="mt-2">
          {isAddingTask ? (
            <div className="flex items-center gap-2">
              <Input
                ref={newTaskInputRef}
                value={newTaskTitle}
                onChange={(e) => setNewTaskTitle(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleAddTask();
                  } else if (e.key === 'Escape') {
                    setNewTaskTitle("");
                    setIsAddingTask(false);
                  }
                }}
                placeholder="Enter task title..."
                className="h-8 text-sm"
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={handleAddTask}
                disabled={!newTaskTitle.trim()}
                className="h-8 w-8 p-0"
              >
                <Check className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setNewTaskTitle("");
                  setIsAddingTask(false);
                }}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsAddingTask(true)}
              className="h-8 px-3 text-sm text-muted-foreground hover:text-foreground border-2 border-dashed border-muted-foreground/20 hover:border-muted-foreground/40 bg-transparent hover:bg-muted/20 w-full justify-start"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add task
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
